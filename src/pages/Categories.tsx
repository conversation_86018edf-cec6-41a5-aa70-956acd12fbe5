
import { Link } from "react-router-dom";
import { ArrowRight, Home, Building, Lightbulb, Factory } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const Categories = () => {
  const categories = [
    {
      id: "indoor",
      name: "Indoor Lighting",
      description: "Modern solutions for residential and commercial interior spaces",
      icon: <Home className="w-12 h-12" />,
      image: "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=600&h=400&fit=crop",
      products: "25+ Products",
      features: ["Panel Lights", "Track Systems", "Pendant Lights", "Recessed Fixtures"],
      link: "/products?category=Indoor"
    },
    {
      id: "outdoor",
      name: "Outdoor Lighting",
      description: "Weather-resistant solutions for exterior and landscape applications",
      icon: <Building className="w-12 h-12" />,
      image: "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=600&h=400&fit=crop",
      products: "18+ Products",
      features: ["Wall Washers", "Spike Lights", "Bollards", "Flood Lights"],
      link: "/products?category=Outdoor"
    },
    {
      id: "profiles",
      name: "LED Profiles",
      description: "Professional aluminum profiles and mounting systems for LED strips",
      icon: <Lightbulb className="w-12 h-12" />,
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop",
      products: "12+ Products",
      features: ["Surface Mount", "Recessed Profiles", "Corner Profiles", "Suspended Systems"],
      link: "/products?category=Profiles"
    },
    {
      id: "industrial",
      name: "Industrial Lighting",
      description: "Heavy-duty lighting solutions for warehouses and industrial facilities",
      icon: <Factory className="w-12 h-12" />,
      image: "https://images.unsplash.com/photo-1565814329452-e1efa11c5b89?w=600&h=400&fit=crop",
      products: "15+ Products",
      features: ["High Bay Lights", "Linear Fixtures", "Emergency Lighting", "Explosion Proof"],
      link: "/products?category=Industrial"
    }
  ];

  return (
    <div className="pt-16">
      {/* Header Section */}
      <section className="py-20 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Product Categories</h1>
          <p className="text-xl text-gray-300">
            Explore our comprehensive range of professional lighting solutions
          </p>
        </div>
      </section>

      {/* Categories Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {categories.map((category, index) => (
              <Card 
                key={category.id} 
                className="overflow-hidden hover:shadow-2xl transition-all duration-300 group"
              >
                <div className="md:flex">
                  <div className="md:w-1/2 relative overflow-hidden">
                    <img 
                      src={category.image} 
                      alt={category.name}
                      className="w-full h-64 md:h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-all duration-300" />
                    <div className="absolute top-6 left-6 text-white">
                      {category.icon}
                    </div>
                  </div>
                  
                  <CardContent className="md:w-1/2 p-8 flex flex-col justify-between">
                    <div>
                      <h3 className="text-2xl font-bold text-black mb-3">{category.name}</h3>
                      <p className="text-gray-600 mb-4">{category.description}</p>
                      
                      <div className="mb-6">
                        <span className="text-sm font-semibold text-black bg-gray-100 px-3 py-1 rounded-full">
                          {category.products}
                        </span>
                      </div>
                      
                      <div className="mb-6">
                        <h4 className="font-semibold text-black mb-2">Product Types:</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          {category.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center">
                              <div className="w-1.5 h-1.5 bg-black rounded-full mr-2" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    
                    <Button asChild className="bg-black hover:bg-gray-800 text-white">
                      <Link to={category.link}>
                        View Products
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-black mb-4">Need Custom Solutions?</h2>
          <p className="text-lg text-gray-600 mb-8">
            Our engineering team can develop bespoke lighting solutions tailored to your specific requirements
          </p>
          <Button asChild size="lg" className="bg-black hover:bg-gray-800 text-white">
            <Link to="/contact">
              Contact Our Experts
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Categories;
