
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { ArrowRight, Lightbulb, Building, Factory, Home } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CategoryCard, CategoryCardSkeleton } from "@/components/ui/category";
import { ProductGridSkeleton } from "@/components/ui/product-skeleton";
import { fetchCategories } from "@/lib/data-fetch";
import { useIntersectionObserver, useStaggeredAnimation } from "@/hooks/useIntersectionObserver";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';

interface Product {
  id: string;
  name: string;
  category: string;
  shortDescription: string;
  images: string[];
}

const Index = () => {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [isProductsLoading, setIsProductsLoading] = useState(true);
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(true);
  const [categories, setCategories] = useState<any[]>([]);

  // Hero section staggered animations
  const { ref: titleRef, shouldAnimate: titleAnimate } = useStaggeredAnimation<HTMLHeadingElement>(0);
  const { ref: subtitleRef, shouldAnimate: subtitleAnimate } = useStaggeredAnimation<HTMLParagraphElement>(200);
  const { ref: buttonsRef, shouldAnimate: buttonsAnimate } = useStaggeredAnimation<HTMLDivElement>(400);

  // Section animations
  const { ref: productsRef, isIntersecting: productsVisible } = useIntersectionObserver<HTMLElement>();
  const { ref: categoriesRef, isIntersecting: categoriesVisible } = useIntersectionObserver<HTMLElement>();
  const { ref: ctaRef, isIntersecting: ctaVisible } = useIntersectionObserver<HTMLElement>();

  useEffect(() => {
    // Load featured products
    fetch("/data/products.json")
      .then((res) => res.json())
      .then((products: Product[]) => {
        setFeaturedProducts(products.slice(0, 3));
        setIsProductsLoading(false);
      })
      .catch((error) => {
        console.error("Error loading products:", error);
        setIsProductsLoading(false);
      });
  }, []);

  useEffect(() => {
    fetchCategories()
      .then((data) => {
        setCategories(data);
        setIsCategoriesLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching categories:", error);
        setIsCategoriesLoading(false);
      });
  }, []);


  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-r from-black to-gray-900 text-white">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=1920&h=1080&fit=crop')"
          }}
        />
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1
            ref={titleRef}
            className={`text-5xl md:text-7xl font-bold mb-6 transition-all duration-700 ease-out ${titleAnimate
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-8'
              }`}
          >
            Premium Architectural Lighting
          </h1>
          <p
            ref={subtitleRef}
            className={`text-xl md:text-2xl mb-8 text-gray-300 transition-all duration-700 ease-out ${subtitleAnimate
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-8'
              }`}
          >
            Illuminating excellence with cutting-edge LED technology and innovative design solutions
          </p>
          <div
            ref={buttonsRef}
            className={`flex flex-col sm:flex-row gap-4 justify-center transition-all duration-700 ease-out ${buttonsAnimate
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-8'
              }`}
          >
            <Button asChild size="lg" className="bg-white text-black hover:bg-gray-700 hover:text-white transition-all duration-300 hover:scale-105 hover:shadow-lg">
              <Link to="/categories">
                Explore Products
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-black hover:bg-black hover:text-white transition-all duration-300 hover:scale-105 hover:shadow-lg">
              <Link to="/contact">Get Quote</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Brand Intro */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-black mb-6">Lighting Innovation Since 2010</h2>
          <p className="text-lg text-gray-600 leading-relaxed">
            LumenX combines advanced LED technology with sophisticated design to create lighting solutions
            that transform spaces. From residential interiors to large-scale commercial projects,
            we deliver excellence in every fixture.
          </p>
        </div>
      </section>

      {/* Featured Categories */}
      <section ref={categoriesRef} className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className={`text-center mb-16 transition-all duration-800 ease-out ${categoriesVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}>
            <h2 className="text-4xl font-bold text-black mb-4">Our Categories</h2>
            <p className="text-lg text-gray-600">Discover our comprehensive range of lighting solutions</p>
          </div>

          <div className="mt-8">
            {isCategoriesLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="animate-pulse"
                    style={{ animationDelay: `${i * 100}ms` }}
                  >
                    <CategoryCardSkeleton />
                  </div>
                ))}
              </div>
            ) : (
              <div className={`relative transition-all duration-800 ease-out ${categoriesVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                }`}>
                <Swiper
                  modules={[Navigation]}
                  spaceBetween={30}
                  slidesPerView={1}
                  navigation={{
                    nextEl: '.swiper-button-next-custom',
                    prevEl: '.swiper-button-prev-custom',
                  }}
                  breakpoints={{
                    640: {
                      slidesPerView: 2,
                    },
                    1024: {
                      slidesPerView: 3,
                    },
                    1280: {
                      slidesPerView: 4,
                    },
                  }}
                  className="pb-12"
                >
                  {categories.map((category, index) => (
                    <SwiperSlide key={index}>
                      <div
                        className={`transition-all duration-600 ease-out ${categoriesVisible
                          ? 'opacity-100 translate-y-0'
                          : 'opacity-0 translate-y-8'
                          }`}
                        style={{
                          transitionDelay: categoriesVisible ? `${index * 100}ms` : '0ms'
                        }}
                      >
                        <CategoryCard category={{ ...category, id: category.name.toLowerCase() }} />
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>
                <Button
                  className="swiper-button-prev-custom absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white hover:scale-110 text-black rounded-full p-2 shadow-lg hover:shadow-xl -ml-4 transition-all duration-300"
                  size="icon"
                  variant="ghost"
                >
                  <ArrowRight className="w-5 h-5 rotate-180" />
                </Button>
                <Button
                  className="swiper-button-next-custom absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white hover:scale-110 text-black rounded-full p-2 shadow-lg hover:shadow-xl -mr-4 transition-all duration-300"
                  size="icon"
                  variant="ghost"
                >
                  <ArrowRight className="w-5 h-5" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section ref={productsRef} className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className={`text-center mb-16 transition-all duration-800 ease-out ${productsVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}>
            <h2 className="text-4xl font-bold text-black mb-4">Featured Products</h2>
            <p className="text-lg text-gray-600">Discover our most popular lighting solutions</p>
          </div>

          {isProductsLoading ? (
            <ProductGridSkeleton count={3} />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {featuredProducts.map((product, index) => (
                <Link
                  key={product.id}
                  to={`/product/${product.id}`}
                  className={`group transition-all duration-600 ease-out ${productsVisible
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                    }`}
                  style={{
                    transitionDelay: productsVisible ? `${index * 150}ms` : '0ms'
                  }}
                >
                  <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 hover:scale-105 group">
                    <div className="relative h-64 overflow-hidden">
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 ease-out"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500" />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-500" />
                    </div>
                    <CardContent className="p-6 relative">
                      <h3 className="text-xl font-semibold text-black mb-2 group-hover:text-gray-800 transition-colors duration-300">{product.name}</h3>
                      <p className="text-gray-600 mb-4 group-hover:text-gray-700 transition-colors duration-300">{product.shortDescription}</p>
                      <Button
                        variant="outline"
                        className="w-full group-hover:bg-black group-hover:text-white group-hover:border-black transition-all duration-300 hover:scale-105"
                      >
                        View Details
                        <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </Button>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section ref={ctaRef} className="py-20 bg-black text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className={`text-4xl font-bold mb-6 transition-all duration-800 ease-out ${ctaVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'
            }`}>
            Ready to Transform Your Space?
          </h2>
          <p className={`text-xl text-gray-300 mb-8 transition-all duration-800 ease-out ${ctaVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'
            }`}
            style={{ transitionDelay: ctaVisible ? '200ms' : '0ms' }}>
            Let our lighting experts help you create the perfect ambiance for your project
          </p>
          <div className={`flex flex-col sm:flex-row gap-4 justify-center transition-all duration-800 ease-out ${ctaVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'
            }`}
            style={{ transitionDelay: ctaVisible ? '400ms' : '0ms' }}>
            <Button asChild size="lg" className="bg-white text-black hover:bg-gray-200 hover:scale-105 transition-all duration-300 hover:shadow-lg">
              <Link to="/contact">
                Get Free Consultation
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
