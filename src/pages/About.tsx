
import { Award, Users, Globe, Zap } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const About = () => {
  const milestones = [
    { year: "2010", event: "LumenX founded with vision for innovative LED lighting" },
    { year: "2012", event: "First major commercial project completed" },
    { year: "2015", event: "International expansion begins" },
    { year: "2018", event: "Smart lighting division established" },
    { year: "2020", event: "Sustainability initiative launched" },
    { year: "2024", event: "Over 10,000 projects completed worldwide" }
  ];

  const stats = [
    { icon: <Globe className="w-8 h-8" />, number: "50+", label: "Countries Served" },
    { icon: <Users className="w-8 h-8" />, number: "500+", label: "Team Members" },
    { icon: <Award className="w-8 h-8" />, number: "25+", label: "Awards Won" },
    { icon: <Zap className="w-8 h-8" />, number: "10M+", label: "Lives Illuminated" }
  ];

  const values = [
    {
      title: "Innovation",
      description: "Constantly pushing the boundaries of lighting technology to create solutions that exceed expectations."
    },
    {
      title: "Quality",
      description: "Every product undergoes rigorous testing to ensure it meets our exceptional standards of performance and durability."
    },
    {
      title: "Sustainability",
      description: "Committed to environmentally responsible practices and energy-efficient solutions that benefit our planet."
    },
    {
      title: "Partnership",
      description: "Building lasting relationships with clients, suppliers, and communities through trust and collaboration."
    }
  ];

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">About LumenX</h1>
          <p className="text-xl text-gray-300">
            Illuminating the world with innovative lighting solutions since 2010
          </p>
        </div>
      </section>

      {/* Company Story */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-black mb-6">Our Story</h2>
              <div className="space-y-4 text-gray-700">
                <p>
                  Founded in 2010 with a vision to revolutionize the lighting industry, LumenX began as a small team 
                  of passionate engineers and designers who believed that lighting could transform spaces and lives.
                </p>
                <p>
                  What started as a local business has grown into a global leader in LED lighting technology, 
                  serving clients across 50+ countries with innovative, sustainable, and high-performance 
                  lighting solutions.
                </p>
                <p>
                  Today, we continue to push the boundaries of what's possible in lighting design, combining 
                  cutting-edge technology with aesthetic excellence to create products that not only illuminate 
                  spaces but inspire people.
                </p>
              </div>
            </div>
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop" 
                alt="LumenX facility"
                className="rounded-lg shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-black text-white rounded-full mb-4">
                  {stat.icon}
                </div>
                <div className="text-3xl font-bold text-black mb-2">{stat.number}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <Card className="border-none shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-black mb-4">Our Mission</h3>
                <p className="text-gray-700 leading-relaxed">
                  To illuminate the world with innovative, sustainable, and high-quality LED lighting solutions 
                  that enhance human experiences and contribute to a more energy-efficient future. We strive to 
                  exceed customer expectations through continuous innovation and exceptional service.
                </p>
              </CardContent>
            </Card>
            
            <Card className="border-none shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-black mb-4">Our Vision</h3>
                <p className="text-gray-700 leading-relaxed">
                  To be the global leader in lighting technology, recognized for transforming spaces and lives 
                  through intelligent, beautiful, and sustainable lighting solutions. We envision a world where 
                  every space is perfectly illuminated to inspire, comfort, and energize.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-black mb-4">Our Values</h2>
            <p className="text-lg text-gray-600">
              The principles that guide everything we do
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="border-none shadow-lg hover:shadow-xl transition-shadow">
                <CardContent className="p-8">
                  <h3 className="text-xl font-bold text-black mb-3">{value.title}</h3>
                  <p className="text-gray-700">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-black mb-4">Our Journey</h2>
            <p className="text-lg text-gray-600">Key milestones in our growth story</p>
          </div>
          
          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <div key={index} className="flex items-center space-x-6">
                <div className="flex-shrink-0 w-20 h-20 bg-black text-white rounded-full flex items-center justify-center font-bold">
                  {milestone.year}
                </div>
                <div className="flex-1">
                  <p className="text-lg text-gray-700">{milestone.event}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications */}
      <section className="py-16 bg-black text-white">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">Certifications & Standards</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-2xl font-bold mb-2">ISO 9001</div>
              <div className="text-gray-300 text-sm">Quality Management</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold mb-2">ISO 14001</div>
              <div className="text-gray-300 text-sm">Environmental Management</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold mb-2">CE</div>
              <div className="text-gray-300 text-sm">European Conformity</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold mb-2">RoHS</div>
              <div className="text-gray-300 text-sm">Restriction of Hazardous Substances</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
