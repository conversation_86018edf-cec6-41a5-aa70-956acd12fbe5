
import { useState, useEffect, useMemo } from "react";
import { useSearchParams, Link } from "react-router-dom";
import { Search, Filter, ArrowRight, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Separator } from "@/components/ui/separator";

interface Product {
  id: string;
  name: string;
  category: string;
  shortDescription: string;
  images: string[];
  specs: Record<string, string>;
  "application-area"?: string;
}

interface FilterState {
  productType: string;
  mounting: string;
  applicationArea: string;
  power: number[];
  luminousFlux: number[];
  size: number[];
  dimmingType: string;
  finish: string;
}

const Products = () => {
  const [searchParams] = useSearchParams();
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  
  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    productType: searchParams.get("category") || "All",
    mounting: "All",
    applicationArea: "All",
    power: [0, 100],
    luminousFlux: [0, 15000],
    size: [0, 1000],
    dimmingType: "All",
    finish: "All"
  });

  // Helper function to extract numeric value from string with units
  const extractNumericValue = (value: string): number => {
    if (!value) return 0;
    const match = value.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 0;
  };

  // Get unique filter options from products
  const filterOptions = useMemo(() => {
    if (!products.length) return {
      productTypes: [],
      mountings: [],
      applicationAreas: [],
      dimmingTypes: [],
      finishes: []
    };

    return {
      productTypes: ["All", ...new Set(products.map(p => p.category))],
      mountings: ["All", ...new Set(products.map(p => p.specs.Mounting).filter(Boolean))],
      applicationAreas: ["All", ...new Set(products.map(p => p["application-area"]).filter(Boolean))],
      dimmingTypes: ["All", ...new Set(products.map(p => p.specs.DimmingType).filter(Boolean))],
      finishes: ["All", ...new Set(products.map(p => p.specs.finish).filter(Boolean))]
    };
  }, [products]);

  useEffect(() => {
    // Load products from JSON
    fetch("/data/products.json")
      .then((res) => res.json())
      .then((data: Product[]) => {
        setProducts(data);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error loading products:", error);
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    // Apply all filters
    let filtered = products;

    // Search term filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.shortDescription.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Product type filter
    if (filters.productType !== "All") {
      filtered = filtered.filter(product => product.category === filters.productType);
    }

    // Mounting filter
    if (filters.mounting !== "All") {
      filtered = filtered.filter(product => product.specs.Mounting === filters.mounting);
    }

    // Application area filter
    if (filters.applicationArea !== "All") {
      filtered = filtered.filter(product => product["application-area"] === filters.applicationArea);
    }

    // Power filter
    filtered = filtered.filter(product => {
      const power = extractNumericValue(product.specs.Power || "0");
      return power >= filters.power[0] && power <= filters.power[1];
    });

    // Luminous flux filter
    filtered = filtered.filter(product => {
      const luminousFlux = extractNumericValue(product.specs.Luminousflex || "0");
      return luminousFlux >= filters.luminousFlux[0] && luminousFlux <= filters.luminousFlux[1];
    });

    // Size filter
    filtered = filtered.filter(product => {
      const size = extractNumericValue(product.specs.size || "0");
      return size >= filters.size[0] && size <= filters.size[1];
    });

    // Dimming type filter
    if (filters.dimmingType !== "All") {
      filtered = filtered.filter(product => product.specs.DimmingType === filters.dimmingType);
    }

    // Finish filter
    if (filters.finish !== "All") {
      filtered = filtered.filter(product => product.specs.finish === filters.finish);
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, filters]);

  const clearAllFilters = () => {
    setFilters({
      productType: "All",
      mounting: "All",
      applicationArea: "All",
      power: [0, 100],
      luminousFlux: [0, 15000],
      size: [0, 1000],
      dimmingType: "All",
      finish: "All"
    });
    setSearchTerm("");
  };

  if (loading) {
    return (
      <div className="pt-16 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading products...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-16">
      {/* Header */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-6">
            <h1 className="text-4xl font-bold text-black mb-4">
              {filters.productType !== "All" ? `${filters.productType} Lighting` : "All Products"}
            </h1>
            <p className="text-lg text-gray-600">
              Discover our comprehensive range of professional lighting solutions
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-md mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search products..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Main Content with Filter Panel */}
      <section className="py-8 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left Filter Panel */}
            <div className="lg:w-80 flex-shrink-0">
              <div className="bg-gray-50 rounded-lg p-6 sticky top-24">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-black">Filters</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="text-sm text-gray-500 hover:text-black"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Clear All
                  </Button>
                </div>

                <div className="space-y-6">
                  {/* Product Type Filter */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Product Type</label>
                    <Select
                      value={filters.productType}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, productType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select product type" />
                      </SelectTrigger>
                      <SelectContent>
                        {filterOptions.productTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  {/* Mounting Filter */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Mounting</label>
                    <Select
                      value={filters.mounting}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, mounting: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select mounting type" />
                      </SelectTrigger>
                      <SelectContent>
                        {filterOptions.mountings.map((mounting) => (
                          <SelectItem key={mounting} value={mounting}>
                            {mounting}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  {/* Application Area Filter */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Application Area</label>
                    <Select
                      value={filters.applicationArea}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, applicationArea: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select application area" />
                      </SelectTrigger>
                      <SelectContent>
                        {filterOptions.applicationAreas.map((area) => (
                          <SelectItem key={area} value={area}>
                            {area}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  {/* Power Filter */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Power: {filters.power[0]}W - {filters.power[1]}W
                    </label>
                    <Slider
                      value={filters.power}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, power: value }))}
                      max={100}
                      min={0}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  <Separator />

                  {/* Luminous Flux Filter */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Luminous Flux: {filters.luminousFlux[0]}lm - {filters.luminousFlux[1]}lm
                    </label>
                    <Slider
                      value={filters.luminousFlux}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, luminousFlux: value }))}
                      max={15000}
                      min={0}
                      step={500}
                      className="mt-2"
                    />
                  </div>

                  <Separator />

                  {/* Size Filter */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">
                      Size: {filters.size[0]}mm - {filters.size[1]}mm
                    </label>
                    <Slider
                      value={filters.size}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, size: value }))}
                      max={1000}
                      min={0}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  <Separator />

                  {/* Dimming Type Filter */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Dimming Type</label>
                    <Select
                      value={filters.dimmingType}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, dimmingType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select dimming type" />
                      </SelectTrigger>
                      <SelectContent>
                        {filterOptions.dimmingTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  {/* Finish Filter */}
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Finish</label>
                    <Select
                      value={filters.finish}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, finish: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select finish" />
                      </SelectTrigger>
                      <SelectContent>
                        {filterOptions.finishes.map((finish) => (
                          <SelectItem key={finish} value={finish}>
                            {finish}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Content Area */}
            <div className="flex-1">
              {filteredProducts.length === 0 ? (
                <div className="text-center py-16">
                  <div className="text-gray-400 mb-4">
                    <Filter className="w-16 h-16 mx-auto mb-4" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">No products found</h3>
                  <p className="text-gray-500">Try adjusting your search or filter criteria</p>
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-center mb-6">
                    <p className="text-gray-600">
                      Showing {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    {filteredProducts.map((product) => (
                      <Card key={product.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 group hover:-translate-y-1">
                        <div className="relative h-64 overflow-hidden">
                          <img 
                            src={product.images[0]} 
                            alt={product.name}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300" />
                          <div className="absolute top-4 right-4">
                            <span className="bg-black text-white px-2 py-1 text-xs font-medium rounded">
                              {product.category}
                            </span>
                          </div>
                        </div>
                        
                        <CardContent className="p-6">
                          <h3 className="text-xl font-semibold text-black mb-2">{product.name}</h3>
                          <p className="text-gray-600 mb-4 line-clamp-2">{product.shortDescription}</p>
                          
                          <div className="mb-4">
                            <div className="flex flex-wrap gap-2">
                              {Object.entries(product.specs).slice(0, 2).map(([key, value]) => (
                                <span key={key} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                                  {key}: {value}
                                </span>
                              ))}
                            </div>
                          </div>
                          
                          <div className="flex gap-2">
                            <Button asChild variant="outline" className="flex-1 group-hover:bg-black group-hover:text-white transition-colors">
                              <Link to={`/product/${product.id}`}>
                                View Details
                                <ArrowRight className="ml-2 w-4 h-4" />
                              </Link>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Products;
