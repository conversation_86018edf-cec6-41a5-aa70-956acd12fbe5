import { Card, CardContent } from "./card";

export function ProductCardSkeleton() {
  return (
    <Card className="overflow-hidden">
      <div className="relative h-64 overflow-hidden">
        <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer bg-[length:200px_100%]" />
      </div>
      <CardContent className="p-6">
        <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer bg-[length:200px_100%] mb-2 rounded" />
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer bg-[length:200px_100%] mb-4 rounded w-3/4" />
        <div className="h-10 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer bg-[length:200px_100%] rounded" />
      </CardContent>
    </Card>
  );
}

export function ProductGridSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {Array.from({ length: count }).map((_, i) => (
        <ProductCardSkeleton key={i} />
      ))}
    </div>
  );
}
