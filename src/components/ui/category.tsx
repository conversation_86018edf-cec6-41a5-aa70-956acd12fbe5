import { Link } from "react-router-dom";
import { ArrowRight } from "lucide-react";
import { Card, CardContent } from "./card";

interface Category {
    name: string;
    description: string;
    image_url: string;
    id: string;
}

export function CategoryCard({ category }: { category: Category }) {
    return (
        <Link key={category.name} to={`/products?category=${category.id}`} className="group">
            <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 hover:scale-105 group-hover:bg-gray-50">
                <div className="relative h-48 overflow-hidden">
                    <img
                        src={category.image_url}
                        alt={category.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 ease-out"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-60 group-hover:opacity-40 transition-all duration-500" />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-500" />
                    <div className="absolute bottom-4 left-4 right-4">
                        <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2 transform translate-y-2 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500">
                            <p className="text-xs text-gray-800 font-medium">Click to explore</p>
                        </div>
                    </div>
                </div>
                <CardContent className="p-6 relative">
                    <h3 className="text-xl font-semibold text-black mb-2 group-hover:text-gray-800 transition-colors duration-300">{category.name}</h3>
                    <p className="text-gray-600 line-clamp-2 h-12 group-hover:text-gray-700 transition-colors duration-300">{category.description}</p>
                    <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                        <ArrowRight className="w-5 h-5 text-gray-400" />
                    </div>
                </CardContent>
            </Card>
        </Link>
    );
}

export function CategoryCardSkeleton() {
    return (
        <div className="group">
            <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                <div className="relative h-48 overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer bg-[length:200px_100%]" />
                </div>
                <CardContent className="p-6">
                    <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer bg-[length:200px_100%] mb-2 rounded" />
                    <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer bg-[length:200px_100%] rounded w-3/4" />
                </CardContent>
            </Card>
        </div>
    );
}
