import { Link } from "react-router-dom";
import { Card, CardContent } from "./card";

interface Category {
    name: string;
    description: string;
    image_url: string;
    id: string;
}

export function CategoryCard({ category }: { category: Category }) {
    return (
        <Link key={category.name} to={`/products?category=${category.id}`} className="group">
            <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                <div className="relative h-48 overflow-hidden">
                    <img
                        src={category.image_url}
                        alt={category.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    {/* <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-all duration-300" />
                    <div className="absolute top-4 left-4 text-white">
                      {category.icon}
                    </div> */}
                </div>
                <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-black mb-2">{category.name}</h3>
                    <p className="text-gray-600 line-clamp-2 h-12">{category.description}</p>
                </CardContent>
            </Card>
        </Link>
    );
}

export function CategoryCardSkeleton() {
    return (
        <div className="group">
            <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                <div className="relative h-48 overflow-hidden">
                    <div className="w-full h-full bg-gray-200 animate-pulse" />
                </div>
                <CardContent className="p-6">
                    <div className="h-6 bg-gray-200 animate-pulse mb-2" />
                    <div className="h-4 bg-gray-200 animate-pulse" />
                </CardContent>
            </Card>
        </div>
    );
}
