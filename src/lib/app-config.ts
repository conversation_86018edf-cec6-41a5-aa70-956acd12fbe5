import { initializeApp, getApps } from "firebase/app";

const firebaseConfig = {
    apiKey: "AIzaSyCked81OSqjBj7jJ-R7nRj20nmOC0DxRsE",
    authDomain: "lumen-x-srl.firebaseapp.com",
    projectId: "lumen-x-srl",
    storageBucket: "lumen-x-srl.firebasestorage.app",
    messagingSenderId: "269372904611",
    appId: "1:269372904611:web:2fa794a86b50a46f969b16",
    measurementId: "G-Y243VNMK4X"
};

const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

export default function AppConfig() {
    return app;
}
