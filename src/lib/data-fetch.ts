import AppConfig from "./app-config";
import { getFirestore } from "firebase/firestore";
import { collection, query, where, orderBy, getDocs } from "firebase/firestore";

export async function fetchCategories(): Promise<any[]> {
    try {
        const app = AppConfig();
        const db = getFirestore(app);
        const categoriesCollection = collection(db, "categories");
        const q = query(categoriesCollection, where("is_active", "==", true), orderBy("category_id", "asc"));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
        console.error("Error fetching categories:", error);
        return [];
    }
}